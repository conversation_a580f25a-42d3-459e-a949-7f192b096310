{"name": "backend", "version": "1.0.0", "description": "My Moleculer-based microservices project", "scripts": {"dev": "moleculer-runner --hot services/**/*.service.js", "start": "moleculer-runner -e services/**/**/**/*.service.js -e services/**/**/*.service.js -e services/**/*.service.js -e services/*.service.js --hot services/**/*.service.js"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.26.10", "@babel/eslint-parser": "^7.21.3", "eslint": "^9.25.1", "jest": "^29.7.0", "jest-cli": "^29.7.0", "moleculer-repl": "^0.7.4", "prettier": "^3.0.3"}, "dependencies": {"axios": "^1.6.7", "bcryptjs": "^3.0.2", "cookie": "^1.0.2", "cron": "^4.1.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "diff": "^7.0.0", "dotenv": "^16.4.5", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "handlebars": "^4.7.8", "i18next": "^25.0.1", "i18next-fs-backend": "^2.1.1", "jsonwebtoken": "^9.0.0", "lodash": "^4.17.21", "moleculer": "^0.14.26", "moleculer-cron": "^0.0.7", "moleculer-db": "^0.8.20", "moleculer-db-adapter-mongo": "^0.4.15", "moleculer-db-adapter-mongoose": "^0.10.0", "moleculer-web": "^0.10.4", "moment": "^2.29.4", "mongoose": "8.14.0", "mongoose-paginate-v2": "^1.7.1", "qs": "^6.14.0", "resend": "^4.7.0", "sharp": "^0.34.1", "slugify": "^1.6.6", "socket.io": "^4.8.1", "xlsx": "^0.18.5"}, "engines": {"node": ">= 16.x.x"}}